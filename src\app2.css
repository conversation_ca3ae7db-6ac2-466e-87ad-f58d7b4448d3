/* src/app.postcss */

/* 1. Import Tailwind CSS v4 with legacy config for grid utilities */
@config "../tailwind.config.js";
@import "tailwindcss";

/* 2. Define any custom theme properties (CSS variables) if needed */
/*    Most theme extensions are now through CSS variables that <PERSON><PERSON><PERSON> generates
      or by defining custom utilities. */
@theme {
  /* AMQ PLUS color scheme based on #DF6975 */
  --color-amq-primary: #DF6975;
  --color-amq-secondary: #E8899A;
  --color-amq-accent: #75B9DF;
  --color-amq-light: #F5E6E8;
  --color-amq-dark: #B54A5A;
  --color-amq-neutral: #8B7B7A;
  --color-amq-success: #75DF8B;
  --color-amq-warning: #DFB975;

  /* Custom breakpoint for extra small screens */
  --breakpoint-xs: 480px;
}



/* 3. Define global base styles (if not covered by Tailwind's preflight or your needs) */
@layer base {
  body {
    @apply bg-gray-900 text-slate-100; /* Base dark theme */
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
    overflow-x: hidden; /* Prevent horizontal scroll from large blurred elements */
  }
}

/* 4. Define custom component classes (if any) */
@layer components {
  /* .custom-button {
    @apply px-4 py-2 bg-blue-500 text-white rounded;
  } */
}

/* 5. Define custom utilities */
@layer utilities {
  .animation-delay-2000 {
    animation-delay: 2s;
  }
  .animation-delay-4000 {
    animation-delay: 4s;
  }
  .animation-delay-6000 {
    animation-delay: 6s;
  }
  .animation-delay-8000 {
    animation-delay: 8s;
  }

  @keyframes fadeIn {
    from { opacity: 0; transform: translateY(-10px); }
    to { opacity: 1; transform: translateY(0); }
  }

  .animate-fadeIn {
    animation: fadeIn 0.3s ease-out forwards;
  }

  /* Custom pulse animation with larger intervals */
  @keyframes slowPulse {
    0%, 100% {
      opacity: 0.05;
      transform: scale(0.95);
    }
    50% {
      opacity: 0.15;
      transform: scale(1.05);
    }
  }

  @keyframes slowPulse2 {
    0%, 100% {
      opacity: 0.08;
      transform: scale(0.98);
    }
    50% {
      opacity: 0.12;
      transform: scale(1.02);
    }
  }

  @keyframes slowPulse3 {
    0%, 100% {
      opacity: 0.06;
      transform: scale(0.96);
    }
    50% {
      opacity: 0.10;
      transform: scale(1.04);
    }
  }

  .animate-slow-pulse {
    animation: slowPulse 8s ease-in-out infinite;
  }

  .animate-slow-pulse-2 {
    animation: slowPulse2 10s ease-in-out infinite;
  }

  .animate-slow-pulse-3 {
    animation: slowPulse3 12s ease-in-out infinite;
  }

  /* Grid beam animations */
  @keyframes gridBeamAppear {
    0% {
      opacity: 0;
      transform: scale(0.8);
    }
    20% {
      opacity: 1;
      transform: scale(1);
    }
    80% {
      opacity: 1;
      transform: scale(1);
    }
    100% {
      opacity: 0;
      transform: scale(0.8);
    }
  }

  .animate-grid-beam {
    animation: gridBeamAppear 4s ease-in-out;
  }

  /* Aurora text effect with continuous linear animation - Light Blues only */
  .aurora-text {
    background: linear-gradient(
      90deg,
      oklch(70% 0.15 220) 0%,        /* light cyan-blue */
      oklch(68.5% 0.169 237.323) 25%, /* sky-500 (your site blue) */
      oklch(75% 0.18 210) 50%,       /* bright sky blue */
      oklch(65% 0.16 250) 75%,       /* vibrant blue */
      oklch(70% 0.15 220) 100%       /* light cyan-blue - seamless loop */
    );
    background-size: 400% 100%;
    background-clip: text;
    -webkit-background-clip: text;
    color: transparent;
    /* 🎛️ SPEED CONTROL: Change this duration to make it slower/faster */
    animation: aurora-flow 20s linear infinite;
  }

  /* Continuous aurora flow animation - slower linear movement */
  @keyframes aurora-flow {
    0% {
      background-position: 0% center;
    }
    100% {
      background-position: 400% center;
    }
  }

  /* Border beam animation for Magic UI BorderBeam component */
  @keyframes border-beam {
    0% {
      offset-distance: 0%;
    }
    100% {
      offset-distance: 100%;
    }
  }

  .animate-border-beam {
    animation: border-beam var(--duration, 15s) linear infinite;
  }
}